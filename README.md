## 供应链-价格

### 分表配置
1. 创建分表规则类。
例：CompanyStoreShardingAlgorithm
2. 配置文件配置
示例：
```
        sharding-algorithms:
          company-store-algorithm:
            type: CLASS_BASED
            props:
              strategy: COMPLEX
              algorithmClassName: com.yxt.lotprice.infrastructure.config.sharding.CompanyStoreShardingAlgorithm
              virtual_num: 1024
              physical_num: 16
              bulk_threshold: 2
        tables:
          price_adjust_form_item_detail:
            actual-data-nodes: read_write_db.price_adjust_form_item_detail_$->{['1000','2000']}_$->{0..15}
            table-strategy:
              complex:
                sharding-columns: company_code, store_id
                sharding-algorithm-name: company-store-algorithm

```
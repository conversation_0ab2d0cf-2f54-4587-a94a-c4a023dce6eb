<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.yxt.lotprice</groupId>
    <artifactId>yxt-lot-price</artifactId>
    <version>${reversion}</version>
    <name>yxt-lot-price</name>
    <description>yxt mvc 种子项目</description>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.yxt</groupId>
        <artifactId>yxt-xframe</artifactId>
        <version>2.17.1</version>
        <relativePath/>
    </parent>

    <properties>
        <reversion>2.0.0-SNAPSHOT</reversion>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <module.deploy.skip>false</module.deploy.skip>
    </properties>

    <modules>
        <module>yxt-lot-price-sdk</module>
        <module>yxt-lot-price-application</module>
        <module>yxt-lot-price-manager</module>
        <module>yxt-lot-price-infrastructure</module>
        <module>yxt-lot-price-common</module>
        <module>yxt-lot-price-bootstrap</module>
        <module>yxt-lot-price-service</module>
        <module>yxt-lot-price-message-sdk</module>
    </modules>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-application</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-common</artifactId>
                <version>${reversion}</version>
            </dependency>

            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-common-lib</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-common-model</artifactId>
                <version>2.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-service</artifactId>
                <version>${reversion}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>io.github.openfeign</groupId>-->
<!--                <artifactId>feign-core</artifactId>-->
<!--                <version>10.1.0</version>-->
<!--&lt;!&ndash;                <scope>compile</scope>&ndash;&gt;-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-manager</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-infrastructure</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.lotprice</groupId>
                <artifactId>yxt-lot-price-message-sdk</artifactId>
                <version>${reversion}</version>
            </dependency>
            <dependency>
                <groupId>com.yxt.org</groupId>
                <artifactId>yxt-org-read-open-sdk</artifactId>
                <version>1.0.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
    <plugins>
        <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>flatten-maven-plugin</artifactId>
            <version>${flatten-maven-plugin.version}</version>
            <executions>
                <execution>
                    <id>flatten</id>
                    <phase>process-resources</phase>
                    <goals>
                        <goal>flatten</goal>
                    </goals>
                </execution>
                <execution>
                    <id>flatten.clean</id>
                    <phase>clean</phase>
                    <goals>
                        <goal>clean</goal>
                    </goals>
                </execution>
            </executions>
            <configuration>
                <updatePomFile>true</updatePomFile>
                <flattenMode>resolveCiFriendliesOnly</flattenMode>
            </configuration>
        </plugin>
    </plugins>
    </build>
</project>

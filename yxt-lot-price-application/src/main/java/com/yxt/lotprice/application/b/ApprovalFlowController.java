package com.yxt.lotprice.application.b;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.adjustbase.ApprovalFlowService;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSaveDetailReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowPageResp;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowQueryDetailResp;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/b/approval-flow")
@Api(tags = "调价审批流")
@Slf4j
public class ApprovalFlowController extends AbstractController {

    @Autowired
    private ApprovalFlowService approvalFlowService;
    @ApiOperation(value = "分页查询调价单")
    @PostMapping(value = "/page")
    ResponseBase<PageDTO<ApprovalFlowPageResp>> page(@RequestBody @Valid ApprovalFlowPageReq req){
        return ResponseBase.success(approvalFlowService.page(req));
    }

    @ApiOperation(value = "新增/修改调价单")
    @PostMapping(value = "/save")
    ResponseBase<Long> save(@RequestBody @Valid ApprovalFlowSaveDetailReq req){
        return ResponseBase.success();
    }

    @ApiOperation(value = "查询调价单详情")
    @PostMapping(value = "/detail/{id}")
    ResponseBase<PageDTO<ApprovalFlowQueryDetailResp>> detail(@RequestBody @Valid @PathVariable("id") String id){
        return ResponseBase.success();
    }

}

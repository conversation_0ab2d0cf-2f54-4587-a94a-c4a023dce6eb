package com.yxt.lotprice.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:05
 */
@RestController
@RequestMapping(value = "/b/priceAdjustRoleConfig")
@Api(tags = "条价范围配置")
@Slf4j
public class PriceAdjustRoleConfigController extends AbstractController {
    @Resource
    private PriceAdjustRoleConfigService priceAdjustRoleConfigService;

    @ApiOperation(value = "调价范围列表查询")
    @PostMapping(value = "/priceAdjustRangePage")
    ResponseBase<PageDTO<PriceAdjustRangePageResp>> priceAdjustRangePage(@RequestBody PriceAdjustRangePageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.priceAdjustRangePage(req));
    }
    @ApiOperation(value = "报批人配置列表")
    @PostMapping(value = "/submitterPage")
    ResponseBase<PageDTO<SubmitterPageResp>> submitterPage(@RequestBody SubmitterPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.submitterPage(req));
    }
    @ApiOperation(value = "新增编辑报批人")
    @PostMapping(value = "/addOrUpdateSubmitter")
    ResponseBase<Boolean> addOrUpdateSubmitter(@RequestBody AddOrEditSubmitterReq req, @RequestHeader("userName") String userName){
        return ResponseBase.success(priceAdjustRoleConfigService.addSubmitter(req,userName));
    }

    @ApiOperation(value = "新增编辑审批人")
    @PostMapping(value = "/addOrUpdateApprover")
    ResponseBase<Boolean> addOrUpdateApprover(@RequestBody AddOrEditApproverReq req, @RequestHeader("userName") String userName){
        return ResponseBase.success(priceAdjustRoleConfigService.addApprover(req,userName));
    }

    @ApiOperation(value = "审批人配置列表")
    @PostMapping(value = "/approverPage")
    ResponseBase<PageDTO<ApproverPageResp>> approverPage(@RequestBody ApproverPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.approverPage(req));
    }

    @ApiOperation(value = "审批人配置详情")
    @GetMapping(value = "/approverDetail/{code}")
    ResponseBase<ApproverDetailResp> approverDetail(@PathVariable("code") String code){
        return ResponseBase.success(priceAdjustRoleConfigService.approverDetail(code));
    }

    @ApiOperation(value = "报批人配置详情")
    @GetMapping(value = "/submitterDetail/{code}")
    ResponseBase<SubmitterDetailResp> submitterDetail(@PathVariable("code") String code){
        return ResponseBase.success(priceAdjustRoleConfigService.submitterDetail(code));
    }
}

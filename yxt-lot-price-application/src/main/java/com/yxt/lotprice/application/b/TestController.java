package com.yxt.lotprice.application.b;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.PriceAdjustFormItemDetailService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Since: 2025/05/20 10:13
 * Author: qs
 */

@RestController
@RequestMapping(value = "/b/test")
@Slf4j
public class TestController {

    @Resource
    private PriceAdjustFormItemDetailService priceAdjustFormItemDetailService;

    @PostMapping(value = "/ttt")
    ResponseBase<Void> page(@RequestBody Map<String, List<String>> map){

        priceAdjustFormItemDetailService.test(map.get("compycodelist"), map.get("storeIdList"));
        return ResponseBase.success();
    }
}

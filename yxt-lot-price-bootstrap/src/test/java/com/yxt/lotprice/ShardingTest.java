package com.yxt.lotprice;

import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemDetailMapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustResultsMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemDetailPO;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustResultsPO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 分表测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShardingTest {

    @Resource
    private PriceAdjustFormItemDetailMapper priceAdjustFormItemDetailMapper;

    @Resource
    private PriceAdjustResultsMapper priceAdjustResultsMapper;

    /**
     * 测试调价单价格明细分表
     */
    @Test
    public void testPriceAdjustFormItemDetailSharding() {
        // 创建测试数据
        List<PriceAdjustFormItemDetailPO> detailList = new ArrayList<>();
        
        // 创建16个不同store_id的数据，测试分片
        for (int i = 0; i < 16; i++) {
            PriceAdjustFormItemDetailPO detail = new PriceAdjustFormItemDetailPO();
            detail.setFormNo("FORM-" + i);
            detail.setFormItemNo("ITEM-" + i);
            detail.setOrgType("STORE");
            detail.setErpCode("ERP-" + i);
            detail.setStoreId(String.valueOf(i)); // 不同的store_id
            detail.setStoreCode("STORE-" + i);
            detail.setCompanyCode("COMPANY-" + i);
            detail.setPriceType("VIP");
            detail.setPrice(new BigDecimal("100.00"));
            detail.setStartTime(LocalDateTime.now());
            detail.setEndTime(LocalDateTime.now().plusDays(30));
            detail.setAporovalTime(LocalDateTime.now());
            detail.setEnableStatus("VALID");
            detail.setCreatedTime(LocalDateTime.now());
            detail.setUpdatedTime(LocalDateTime.now());
            
            detailList.add(detail);
        }
        
        // 批量插入数据
        for (PriceAdjustFormItemDetailPO detail : detailList) {
            priceAdjustFormItemDetailMapper.insert(detail);
        }
        
        System.out.println("调价单价格明细分表测试完成");
    }
    
    /**
     * 测试调价计算结果分表
     */
    @Test
    public void testPriceAdjustResultsSharding() {
        // 创建测试数据
        List<PriceAdjustResultsPO> resultsList = new ArrayList<>();
        
        // 创建16个不同store_id的数据，测试分片
        for (int i = 0; i < 16; i++) {
            PriceAdjustResultsPO result = new PriceAdjustResultsPO();
            result.setErpCode("ERP-" + i);
            result.setStoreId(String.valueOf(i)); // 不同的store_id
            result.setStoreCode("STORE-" + i);
            result.setCompanyCode("COMPANY-" + i);
            result.setPriceType("VIP");
            result.setPrice(new BigDecimal("100.00"));
            result.setFormNo("FORM-" + i);
            result.setFormItemNo("ITEM-" + i);
            result.setOrgType("STORE");
            result.setStartTime(LocalDateTime.now());
            result.setEndTime(LocalDateTime.now().plusDays(30));
            result.setPushPosStatus("PENDING");
            result.setCreatedTime(LocalDateTime.now());
            result.setUpdatedTime(LocalDateTime.now());
            
            resultsList.add(result);
        }
        
        // 批量插入数据
        for (PriceAdjustResultsPO result : resultsList) {
            priceAdjustResultsMapper.insert(result);
        }
        
        System.out.println("调价计算结果分表测试完成");
    }
}

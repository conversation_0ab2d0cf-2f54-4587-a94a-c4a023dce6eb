package com.yxt.lotprice.common.enums.adjustbase;

import lombok.Getter;

@Getter
public enum AdjustDimensionType {
    ADJUSTMENT("定价"),
    PRICING("新品调价"),
    GIFT_PRICING("赠品定价"),
    GIFT_ADJUSTMENT("赠品调价");


    AdjustDimensionType(String name) {
        this.name = name;
    }
    private String name;

    public static AdjustDimensionType getByName(String name) {
        try {
            return AdjustDimensionType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}

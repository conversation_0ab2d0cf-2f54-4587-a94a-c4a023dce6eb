package com.yxt.lotprice.common.enums.adjustbase;

import lombok.Getter;

@Getter
public enum AdjustFlowType {
    LONG("长流程"),
    SHORT("短流程"),
    NONE("不区分长短流程");


    AdjustFlowType(String name) {
        this.name = name;
    }
    private String name;

    public static AdjustFlowType getByName(String name) {
        try {
            return AdjustFlowType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}

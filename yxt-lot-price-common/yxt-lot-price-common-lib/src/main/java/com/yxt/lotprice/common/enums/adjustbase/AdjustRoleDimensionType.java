package com.yxt.lotprice.common.enums.adjustbase;

import lombok.Getter;

@Getter
public enum AdjustRoleDimensionType {
    APPROVER("审批人"),
    SUBMITTER("报批人");


    AdjustRoleDimensionType(String name) {
        this.name = name;
    }
    private String name;

    public static AdjustRoleDimensionType getByName(String name) {
        try {
            return AdjustRoleDimensionType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}

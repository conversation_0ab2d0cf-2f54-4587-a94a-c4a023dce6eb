package com.yxt.lotprice.common.enums.adjustbase;

import lombok.Getter;

@Getter
public enum AdjustRoleType {
    //审批人
    STORE_S_1("定价", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    AREA_S_1("新品调价", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    COMPANY_S_1("子公司商品部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    COMPANY_S_2("子公司采购部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    COMPANY_S_3("子公司运营管理部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    COMPANY_S_4("子公司品类推广部", AdjustScopeType.COMPANY, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_1("子公司品类推广部", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_2("集团商品中心", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_3("鸿翔中药公司商品管理部", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_4("集团采购中心", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_5("集团非药事业部", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_6("集团后台人员", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),
    GROUP_S_8("一心到家科技公司", AdjustScopeType.GROUP, AdjustRoleDimensionType.SUBMITTER,1),

    //报批人
    SUPERIOR_A_10("申请人直接上级", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    BRANRCH_A_1("分部运营管理部经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    BRANRCH_A_2("分部总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),

    COMPANY_A_1("子公司物价管理专人", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    COMPANY_A_2("子公司商品部经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    COMPANY_A_3("子公司商品总监", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    COMPANY_A_4("子公司供应链总监", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    COMPANY_A_5("子公司总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),
    COMPANY_A_6("上级子公司总经理", AdjustScopeType.COMPANY, AdjustRoleDimensionType.APPROVER,1),

    SUPERIOR_A_20("申请人直接上级（主管除外）", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1),
    GROUP_A_1("申请人中心总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1),
    GROUP_A_2("申请人分管总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1),
    GROUP_A_3("集团中心商品总监", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1),
    GROUP_A_4("集团中心品类规划部部长", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1),
    GROUP_A_5("集团运营中心营销管理部部长", AdjustScopeType.GROUP, AdjustRoleDimensionType.APPROVER,1);



    AdjustRoleType(String name, AdjustScopeType scope, AdjustRoleDimensionType roleDimensionType, int order) {
        this.name = name;
        this.scope = scope;
        this.roleDimensionType = roleDimensionType;
        this.order = order;
    }
    private String name;
    private AdjustScopeType scope;
    private AdjustRoleDimensionType roleDimensionType;

    //todo order待设置
    private int order;

    public static AdjustRoleType getBYcode(String code) {
        for (AdjustRoleType value : AdjustRoleType.values()) {
            if (value.name.equals(code)) {
                return value;
            }
        }
        return null;
    }
}

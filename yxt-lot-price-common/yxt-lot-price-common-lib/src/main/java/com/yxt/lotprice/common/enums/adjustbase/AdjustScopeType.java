package com.yxt.lotprice.common.enums.adjustbase;

import lombok.Getter;

@Getter
public enum AdjustScopeType {
    COMPANY("分公司"),
    GROUP("集团");

    AdjustScopeType(String name) {
        this.name = name;
    }
    private String name;

    public static AdjustScopeType getByName(String name) {
        try {
            return AdjustScopeType.valueOf(name);
        }catch (Exception e){
        }
        return null;
    }
}

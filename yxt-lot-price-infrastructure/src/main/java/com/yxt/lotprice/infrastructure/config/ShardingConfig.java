package com.yxt.lotprice.infrastructure.config;

import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.config.algorithm.ShardingSphereAlgorithmConfiguration;
import org.apache.shardingsphere.sharding.api.config.ShardingRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.rule.ShardingTableRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.sharding.StandardShardingStrategyConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * ShardingSphere分表配置
 */
@Configuration
public class ShardingConfig {

    /**
     * 配置分表规则
     * 
     * @return ShardingRuleConfiguration
     */
    @Bean
    public ShardingRuleConfiguration shardingRuleConfiguration() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        
        // 添加price_adjust_form_item_detail_1000表的分片规则
        shardingRuleConfig.getTables().add(getPriceAdjustFormItemDetailTableRuleConfiguration());
        
        // 添加price_adjust_results_1000表的分片规则
        shardingRuleConfig.getTables().add(getPriceAdjustResultsTableRuleConfiguration());
        
        // 配置分片算法
        shardingRuleConfig.setShardingAlgorithms(getShardingAlgorithms());
        
        return shardingRuleConfig;
    }

    /**
     * 配置price_adjust_form_item_detail_1000表的分片规则
     * 
     * @return ShardingTableRuleConfiguration
     */
    private ShardingTableRuleConfiguration getPriceAdjustFormItemDetailTableRuleConfiguration() {
        // 配置price_adjust_form_item_detail_1000表规则
        ShardingTableRuleConfiguration tableRuleConfig = new ShardingTableRuleConfiguration(
                "price_adjust_form_item_detail_1000", 
                "price_adjust_form_item_detail_1000_${0..15}");
        
        // 设置分片策略，使用store_id作为分片键
        tableRuleConfig.setTableShardingStrategy(
                new StandardShardingStrategyConfiguration("store_id", "hash_mod_algorithm"));
        
        return tableRuleConfig;
    }

    /**
     * 配置price_adjust_results_1000表的分片规则
     * 
     * @return ShardingTableRuleConfiguration
     */
    private ShardingTableRuleConfiguration getPriceAdjustResultsTableRuleConfiguration() {
        // 配置price_adjust_results_1000表规则
        ShardingTableRuleConfiguration tableRuleConfig = new ShardingTableRuleConfiguration(
                "price_adjust_results_1000", 
                "price_adjust_results_1000_${0..15}");
        
        // 设置分片策略，使用store_id作为分片键
        tableRuleConfig.setTableShardingStrategy(
                new StandardShardingStrategyConfiguration("store_id", "hash_mod_algorithm"));
        
        return tableRuleConfig;
    }

    /**
     * 配置分片算法
     * 
     * @return 分片算法配置Map
     */
    private Map<String, ShardingSphereAlgorithmConfiguration> getShardingAlgorithms() {
        Map<String, ShardingSphereAlgorithmConfiguration> shardingAlgorithms = new HashMap<>();
        
        // 配置哈希取模算法
        Properties hashModProperties = new Properties();
        hashModProperties.setProperty("sharding-count", "16");
        shardingAlgorithms.put("hash_mod_algorithm", 
                new ShardingSphereAlgorithmConfiguration("HASH_MOD", hashModProperties));
        
        return shardingAlgorithms;
    }
}

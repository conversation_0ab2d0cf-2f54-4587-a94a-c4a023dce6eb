package com.yxt.lotprice.infrastructure.config.sharding;

import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Properties;


/**
 *
 * sharding逻辑通用，针对不同的分表，继承该类，并实现getPhysicalNum()、getVirtualNum()方法
 * 再在application.yml中配置sharding-algorithms.test-sharding-algorithm.props.strategy: STANDARD
 *
 */
@Slf4j
public abstract class AbstractMurmurPreciseTableRule implements StandardShardingAlgorithm<Long> {


    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {

        // MurmurHash3散列计算
        long hash = Hashing.murmur3_128().hashString(shardingValue.getValue().toString(), Charset.defaultCharset()).asLong();

        // 处理Long.MIN_VALUE的绝对值问题
        long positiveHash = hash == Long.MIN_VALUE ? Long.MAX_VALUE : Math.abs(hash);

        // 计算物理表索引
        long index = (positiveHash % getVirtualNum()) /
                (getVirtualNum() / getPhysicalNum());

        // 匹配目标表（优化查找性能）
        String suffix = "_" + index;
        return availableTargetNames.stream()
                .filter(name -> name.endsWith(suffix))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(
                        String.format("No table found for value %s, index %d", shardingValue.getValue(), index)));
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<Long> shardingValue) {
        // 范围查询：默认返回所有表（根据业务调整）
        log.warn("Range sharding is not fully supported, returning all available tables");
        return availableTargetNames;
    }

    @Override
    public Properties getProps() {
        return new Properties();
    }

    @Override
    public void init(Properties properties) {
        // 初始化时可预加载配置
    }

    /**
     * 借鉴一致性hash虚拟节点思想，虚拟表数量默认1024即可（假设未来分表数量不可能超过1024）
     * 规范参考：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24623647
     * */
    public abstract int getPhysicalNum();

    /**
     * 真实物理分表数量
     */
    public abstract int getVirtualNum();
}
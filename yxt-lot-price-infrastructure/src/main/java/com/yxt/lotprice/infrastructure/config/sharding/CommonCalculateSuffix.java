package com.yxt.lotprice.infrastructure.config.sharding;

import cn.hutool.core.util.HashUtil;
import org.apache.curator.shaded.com.google.common.hash.Hashing;

import java.nio.charset.Charset;

/**
 * 通用计算工具
 * <a href="https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24623647">分表规范</a>
 * Since: 2025/05/20 16:26
 * Author: qs
 */
public class CommonCalculateSuffix {

    public static int calculateTableSuffix(String key, int virtualNum, int physicalNum) {
        return  (int) Math.abs((HashUtil.murmur64(key.getBytes()) % virtualNum) / (virtualNum / physicalNum));
    }
}

package com.yxt.lotprice.infrastructure.config.sharding;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.util.Collection;
import java.util.Collections;
import java.util.Properties;

/**
 * Since: 2025/05/20 18:00
 * Author: qs
 */
public class CommonShardingAlgorithm extends AbstractShardingAlgorithm implements StandardShardingAlgorithm<String> {

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<String> preciseShardingValue) {
        return "";
    }

    @Override
    public Collection<String> doSharding(Collection<String> collection, RangeShardingValue<String> rangeShardingValue) {
        return Collections.emptyList();
    }


    @Override
    public Properties getProps() {
        return super.getProps();
    }

    @Override
    public void init(Properties properties) {
        super.init(properties);
    }
}

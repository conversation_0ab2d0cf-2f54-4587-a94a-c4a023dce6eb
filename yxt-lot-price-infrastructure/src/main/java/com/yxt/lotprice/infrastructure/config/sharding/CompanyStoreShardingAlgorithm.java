package com.yxt.lotprice.infrastructure.config.sharding;

import com.yxt.lang.util.ExLogger;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Since: 2025/05/20 14:23
 * Author: qs
 */
public class CompanyStoreShardingAlgorithm extends AbstractShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> complexKeysShardingValue) {
        // 获取分片字段值
        Map<String, Collection<String>> columnValues = complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        Collection<String> companyCodes = columnValues.get("company_code");
        Collection<String> storeIds = columnValues.get("store_id");

        // 如果没有提供分片值，返回所有表（应避免这种情况）
        if (CollectionUtils.isEmpty(companyCodes) && CollectionUtils.isEmpty(storeIds)) {
            return availableTargetNames;
        }

        // 判断是否触发批量模式
        if (storeIds.size() > bulkThreshold) {
            ExLogger.logger().debug("CompanyStoreShardingAlgorithm storeId 分表键值过多，触发批量路由模式");
            return getAllPossibleTables(availableTargetNames, complexKeysShardingValue.getLogicTableName(), companyCodes);
        }

        return getSpecificTables(availableTargetNames, complexKeysShardingValue.getLogicTableName(), companyCodes, storeIds);
    }

    /**
     * 获取分公司的所有表
     * @param availableTargetNames 所有物理表名
     * @param logicTableName 逻辑表名
     * @param companyCodes 公司编码集合
     * @return 路由到的所有表
     */
    private Collection<String> getAllPossibleTables(Collection<String> availableTargetNames, String logicTableName, Collection<String> companyCodes) {
        return companyCodes.stream()
                .flatMap(companyCode ->
                        IntStream.range(0, physicalNum)
                                .mapToObj(i -> logicTableName + "_" + companyCode + "_" + i)
                )
                .filter(availableTargetNames::contains)
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    /**
     * 获取指定公司指定门店的表
     * @param availableTargetNames 所有物理表名
     * @param logicTableName 逻辑表名
     * @param companyCodes 公司编码集合
     * @param storeIds 门店id集合
     * @return 路由到的所有表
     */
    private Collection<String> getSpecificTables(Collection<String> availableTargetNames, String logicTableName
            , Collection<String> companyCodes, Collection<String> storeIds) {
        Set<String> availableSet = new HashSet<>(availableTargetNames);

        return companyCodes.stream()
                .flatMap(companyCode -> storeIds.stream()
                        .map(storeId -> logicTableName + "_" + companyCode + "_" + CommonCalculateSuffix.calculateTableSuffix(storeId, virtualNum, physicalNum))
                )
                .filter(availableSet::contains)
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    @Override
    public Properties getProps() {
        return super.getProps();
    }

    @Override
    public void init(Properties properties) {
        super.init(properties);
    }
}

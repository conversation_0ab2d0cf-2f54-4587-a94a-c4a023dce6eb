package com.yxt.lotprice.infrastructure.config.sharding;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class ShardingTableRuleConfig {

    /**
     * 借鉴一致性hash虚拟节点思想，虚拟表数量默认1024即可（假设未来分表数量不可能超过1024）
     * 规范参考：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24623647
     * */
    private Integer virtualTableNum=1024;

    /**
     * 真实物理分表数量
     */
    @Value("${integral_tables_number:3}")
    private Integer physicalTableNum;

}

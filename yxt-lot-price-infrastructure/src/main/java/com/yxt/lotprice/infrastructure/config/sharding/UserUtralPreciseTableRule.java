package com.yxt.lotprice.infrastructure.config.sharding;

import java.util.Properties;

public class UserUtralPreciseTableRule extends AbstractMurmurPreciseTableRule{

    @Override
    public Properties getProps() {
        return super.getProps();
    }

    @Override
    public void init(Properties properties) {
        super.init(properties);
    }

    /**
     * 借鉴一致性hash虚拟节点思想，虚拟表数量默认1024即可（假设未来分表数量不可能超过1024）
     * 规范参考：https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=24623647
     * */
    @Override
    public int getPhysicalNum() {
        return 10;
    }


    /**
     * 真实物理分表数量
     */
    @Override
    public int getVirtualNum() {
        return 1024;
    }
}

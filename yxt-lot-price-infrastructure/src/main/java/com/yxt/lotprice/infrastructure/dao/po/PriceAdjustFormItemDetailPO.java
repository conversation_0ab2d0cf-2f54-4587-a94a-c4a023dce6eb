package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 调价单价格明细
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_form_item_detail")
public class PriceAdjustFormItemDetailPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 调价单编码
     */
    private String formNo;

    /**
     * 调价单明细编码
     */
    private String formItemNo;

    /**
     * 机构类型：分公司，价格组，门店
     */
    private String orgType;

    /**
     * ERP商品编码
     */
    private String erpCode;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 价格类型：（会员价，零售价，慢病价）
     */
    private String priceType;

    /**
     * 审批通过调/定价格
     */
    private BigDecimal price;

    /**
     * 价格生效开始时间 包含
     */
    private LocalDateTime startTime;

    /**
     * 价格生效结束时间 包含
     */
    private LocalDateTime endTime;

    /**
     * 审批通过时间
     */
    private LocalDateTime aporovalTime;

    /**
     * 调价单可用状态 作废 未作废
     */
    private String enableStatus;

    /**
     * 作废时间
     */
    private LocalDateTime invalidTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}

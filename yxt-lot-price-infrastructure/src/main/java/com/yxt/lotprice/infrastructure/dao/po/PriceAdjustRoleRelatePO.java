package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 调价角色和分公司/集团的关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_role_relate")
public class PriceAdjustRoleRelatePO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 报批人/审批人配置名称
     */
    private String name;

    /**
     * 报批人/审批人配置编码
     */
    private String code;

    /**
     * 项目属性：GROUP-集团，COMPANY-子公司
     */
    private String scopeName;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 报批人/审批人角色编码
     */
    private String roleCode;

    /**
     * 报批人/审批人类型
     */
    private String type;

    /**
     * 备注
     */
    private String desc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 修改人
     */
    private String modifyName;

}

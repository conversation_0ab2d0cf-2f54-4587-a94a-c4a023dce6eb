<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemDetailMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_form_item_detail_1000_0 (
        id,
        form_no,
        form_item_no,
        org_type,
        erp_code,
        store_id,
        store_code,
        company_code,
        price_type,
        price,
        start_time,
        end_time,
        aporoval_time,
        enable_status,
        invalid_time,
        created_time,
        updated_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.formNo},
        #{item.formItemNo},
        #{item.orgType},
        #{item.erpCode},
        #{item.storeId},
        #{item.storeCode},
        #{item.companyCode},
        #{item.priceType},
        #{item.price},
        #{item.startTime},
        #{item.endTime},
        #{item.aporovalTime},
        #{item.enableStatus},
        #{item.invalidTime},
        #{item.createdTime},
        #{item.updatedTime}
        )
        </foreach>
    </insert>
</mapper>

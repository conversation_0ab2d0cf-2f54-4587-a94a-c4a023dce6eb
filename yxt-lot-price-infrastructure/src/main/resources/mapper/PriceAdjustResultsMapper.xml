<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustResultsMapper">
    

    <!--  -->

    <insert id="insertBatch">
        insert into price_adjust_results_1000_0 (
        id,
        erp_code,
        store_id,
        store_code,
        company_code,
        price_type,
        price,
        form_no,
        form_item_no,
        org_type,
        start_time,
        end_time,
        push_pos_status,
        push_pos_log,
        created_time,
        updated_time
        )
        values
        <foreach collection="records" item="item" index="index" separator=",">
        (
        #{item.id},
        #{item.erpCode},
        #{item.storeId},
        #{item.storeCode},
        #{item.companyCode},
        #{item.priceType},
        #{item.price},
        #{item.formNo},
        #{item.formItemNo},
        #{item.orgType},
        #{item.startTime},
        #{item.endTime},
        #{item.pushPosStatus},
        #{item.pushPosLog},
        #{item.createdTime},
        #{item.updatedTime}
        )
        </foreach>
    </insert>
</mapper>

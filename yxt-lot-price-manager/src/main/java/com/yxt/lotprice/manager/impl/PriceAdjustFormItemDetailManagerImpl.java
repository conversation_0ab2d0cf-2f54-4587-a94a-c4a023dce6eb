package com.yxt.lotprice.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemDetailMapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleConfigMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemDetailPO;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Since: 2025/05/20 10:17
 * Author: qs
 */

@Service
public class PriceAdjustFormItemDetailManagerImpl implements PriceAdjustFormItemDetailManager {

    @Resource
    private PriceAdjustFormItemDetailMapper priceAdjustFormItemDetailMapper;
    @Resource
    private PriceAdjustRoleConfigMapper priceAdjustRoleConfigMapper;

    @Override
    public void test(List<String> compycodelist, List<String> storeIdList) {
        List<PriceAdjustFormItemDetailPO> priceAdjustFormItemDetailPOS = priceAdjustFormItemDetailMapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemDetailPO>()
                .in(PriceAdjustFormItemDetailPO::getCompanyCode, compycodelist)
                .in(PriceAdjustFormItemDetailPO::getStoreId, storeIdList)
        );
        System.out.println(priceAdjustFormItemDetailPOS);
    }
}

package com.yxt.lotprice.manager.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleConfigMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleConfigPO;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleConfigManager;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustRangePageReq;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustRangePageResp;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
@Service
@Slf4j
public class PriceAdjustRoleConfigManagerImpl implements PriceAdjustRoleConfigManager {

    @Resource
    private PriceAdjustRoleConfigMapper priceAdjustRoleConfigMapper;


    @Override
    public PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req) {

        LambdaQueryWrapper<PriceAdjustRoleConfigPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(req.getScopeName()),PriceAdjustRoleConfigPO::getScopeName, req.getScopeName());
        queryWrapper.eq(StrUtil.isNotEmpty(req.getRoleCode()),PriceAdjustRoleConfigPO::getRoleCode, req.getRoleCode());
        queryWrapper.eq(StrUtil.isNotEmpty(req.getCompanyCode()),PriceAdjustRoleConfigPO::getCompanyCode, req.getCompanyCode());
        if (StrUtil.isNotEmpty(req.getDimension())){
            queryWrapper.apply("FIND_IN_SET({0},dimension)",req.getDimension());
        }
        if (StrUtil.isNotEmpty(req.getPriceType())){
            queryWrapper.apply("FIND_IN_SET({0},price_type)", req.getPriceType());
        }

        IPage<PriceAdjustRoleConfigPO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page = priceAdjustRoleConfigMapper.selectPage(page, queryWrapper);

        PageDTO<PriceAdjustRangePageResp> pageDTO = new PageDTO<>(req.getCurrentPage(), req.getPageSize());
        pageDTO.setTotalCount(page.getTotal());
        pageDTO.setTotalPage(page.getPages());
        List<PriceAdjustRangePageResp> data = pageDTO.getData();
        for (PriceAdjustRoleConfigPO record : page.getRecords()) {
            PriceAdjustRangePageResp resp = new PriceAdjustRangePageResp();
            BeanUtils.copyProperties(record, resp);
            resp.setDimension(Arrays.asList(record.getDimension().split(",")));
            resp.setPriceType(Arrays.asList(record.getPriceType().split(",")));
            SysOrganizationDTO orgListByUnitCode = OrgInfoCacheUtils.getOrgListByUnitCode(record.getCompanyCode());
            if (orgListByUnitCode != null) {
                resp.setCompanyName(orgListByUnitCode.getOrName());
            }
            data.add(resp);
        }
        pageDTO.setData(data);
        return pageDTO;
    }
}

package com.yxt.lotprice.manager.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleItemRelateMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleItemRelatePO;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.model.dto.b.request.ItemRelateApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.ItemRelateCompanyReq;
import com.yxt.lotprice.service.model.dto.b.response.ItemRelateSubmitterResp;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.RoleTypeEnum;
import com.yxt.lotprice.service.model.enums.ScopeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:53
 */
@Service
@Slf4j
public class PriceAdjustRoleItemRelateManagerImpl implements PriceAdjustRoleItemRelateManager {

    @Resource
    private PriceAdjustRoleItemRelateMapper priceAdjustRoleItemRelateMapper;
    @Override
    public Boolean addSubmitterItem(AddOrEditSubmitterReq req, String userName) {
        for (ItemRelateCompanyReq itemReq : req.getCompanyList()) {
            PriceAdjustRoleItemRelatePO po = new PriceAdjustRoleItemRelatePO();
            po.setParentCode(req.getCode());
            po.setCompanyCode(itemReq.getCompanyCode());
            po.setRoleCode(req.getRoleCode());
            po.setRoleData(itemReq.getRoleData());
            po.setRoleType(RoleTypeEnum.SUBMITTER.getValue());
            po.setCreateName(userName);
            po.setModifyName(userName);
            po.setCreateTime(LocalDateTime.now());
            po.setModifyTime(LocalDateTime.now());
            priceAdjustRoleItemRelateMapper.insert(po);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateSubmitterItem(AddOrEditSubmitterReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, req.getCode());
        priceAdjustRoleItemRelateMapper.delete(wrapper);
        addSubmitterItem(req,  userName);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addApproverItem(AddOrEditApproverReq req, String userName) {
        for (ItemRelateApproverReq itemReq : req.getApproverList()) {
            PriceAdjustRoleItemRelatePO po = new PriceAdjustRoleItemRelatePO();
            po.setParentCode(req.getCode());
            po.setCompanyCode(req.getCompanyCode());
            po.setRoleCode(itemReq.getRoleCode());
            po.setRoleData(itemReq.getRoleData());
            po.setRoleType(RoleTypeEnum.APPROVER.getValue());
            po.setCreateName(userName);
            po.setModifyName(userName);
            po.setCreateTime(LocalDateTime.now());
            po.setModifyTime(LocalDateTime.now());
            priceAdjustRoleItemRelateMapper.insert(po);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateApproverItem(AddOrEditApproverReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleItemRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, req.getCode());
        priceAdjustRoleItemRelateMapper.delete(wrapper);
        addApproverItem(req,  userName);
        return Boolean.TRUE;
    }

    @Override
    public List<ItemRelateSubmitterResp> getSubmitterItemList(String code,String scopeName) {
        List<PriceAdjustRoleItemRelatePO> list = priceAdjustRoleItemRelateMapper.selectList(new LambdaQueryWrapper<PriceAdjustRoleItemRelatePO>().eq(PriceAdjustRoleItemRelatePO::getParentCode, code));
        if (CollUtil.isEmpty(list)){
            throw new YxtBizException("未找到报批人组织配置");
        }
        List<ItemRelateSubmitterResp> itemRelateSubmitterRespList = new ArrayList<>();
        if (ScopeEnum.GROUP.getValue().equals(scopeName)){
            for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : list) {
                ItemRelateSubmitterResp resp = new ItemRelateSubmitterResp();

                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getBranchByUnitCode(priceAdjustRoleItemRelatePO.getRoleData());
                if (org != null) {
                    resp.setOrgName(org.getOrName());
                    resp.setRoleData(org.getOrCode());
                }
                itemRelateSubmitterRespList.add(resp);
            }
        }else {
            for (PriceAdjustRoleItemRelatePO priceAdjustRoleItemRelatePO : list) {
                ItemRelateSubmitterResp resp = new ItemRelateSubmitterResp();
                SysOrganizationDTO company = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getCompanyCode());
                if (company != null) {
                    resp.setCompanyName(company.getOrName());
                    resp.setCompanyCode(company.getOrCode());
                }
                SysOrganizationDTO org = OrgInfoCacheUtils.getStoreListByCode(priceAdjustRoleItemRelatePO.getRoleCode());
                if (org != null) {
                    resp.setCompanyName(org.getOrName());
                    resp.setCompanyCode(org.getOrCode());
                    SysOrganizationDTO branch = OrgInfoCacheUtils.getBranchByUnitCode(org.getUnitCode());
                    if (branch != null) {
                        resp.setBranchName(branch.getOrName());
                        resp.setBranchCode(branch.getOrCode());
                    }
                }
                itemRelateSubmitterRespList.add(resp);
            }
        }

        return itemRelateSubmitterRespList;
    }
}

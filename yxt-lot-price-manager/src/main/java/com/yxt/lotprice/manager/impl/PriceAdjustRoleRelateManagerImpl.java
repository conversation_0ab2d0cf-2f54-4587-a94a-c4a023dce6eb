package com.yxt.lotprice.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustRoleRelateMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustRoleRelatePO;
import com.yxt.lotprice.manager.convert.PageUtil;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleRelateManager;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.model.dto.b.request.ApproverPageReq;
import com.yxt.lotprice.service.model.dto.b.request.SubmitterPageReq;
import com.yxt.lotprice.service.model.dto.b.response.ApproverDetailResp;
import com.yxt.lotprice.service.model.dto.b.response.ApproverPageResp;
import com.yxt.lotprice.service.model.dto.b.response.SubmitterDetailResp;
import com.yxt.lotprice.service.model.dto.b.response.SubmitterPageResp;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.RoleTypeEnum;
import com.yxt.lotprice.service.utils.SequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/19 17:25
 */
@Service
@Slf4j
public class PriceAdjustRoleRelateManagerImpl implements PriceAdjustRoleRelateManager {

    @Resource
    private PriceAdjustRoleRelateMapper priceAdjustRoleRelateMapper;

    @Resource
    private SequenceUtil sequenceUtil;
    @Override
    public PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req) {
        LambdaQueryWrapper<PriceAdjustRoleRelatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(req.getScopeName()),PriceAdjustRoleRelatePO::getScopeName, req.getScopeName())
                .eq(StrUtil.isNotEmpty(req.getRoleCode()),PriceAdjustRoleRelatePO::getRoleCode, req.getRoleCode())
                .like(StrUtil.isNotEmpty(req.getName()),PriceAdjustRoleRelatePO::getName, req.getName());
        queryWrapper.orderByDesc(PriceAdjustRoleRelatePO::getCode);

        IPage<PriceAdjustRoleRelatePO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page = priceAdjustRoleRelateMapper.selectPage(page, queryWrapper);

        return PageUtil.convertToPageDTO(page, SubmitterPageResp.class);
    }

    @Override
    public String addSubmitter(AddOrEditSubmitterReq req,String userName) {
        PriceAdjustRoleRelatePO po = new PriceAdjustRoleRelatePO();
        String bpSequence = sequenceUtil.getBPSequence();
        po.setCode(bpSequence);
        po.setName(req.getName());
        po.setScopeName(req.getScopeName());
        po.setRoleCode(req.getRoleCode());
        po.setDesc(req.getDesc());
        po.setCreateName(userName);
        po.setModifyName(userName);
        po.setCreateTime(LocalDateTime.now());
        po.setModifyTime(LocalDateTime.now());
        po.setType(RoleTypeEnum.SUBMITTER.getValue());
        priceAdjustRoleRelateMapper.insert(po);
        return bpSequence;
    }

    @Override
    public Boolean updateSubmitter(AddOrEditSubmitterReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleRelatePO>().eq(PriceAdjustRoleRelatePO::getCode, req.getCode());
        PriceAdjustRoleRelatePO po = priceAdjustRoleRelateMapper.selectOne(wrapper);
        if (po == null){
            throw new RuntimeException("报批人配置不存在");
        }
        po.setName(req.getName());
        po.setScopeName(req.getScopeName());
        po.setRoleCode(req.getRoleCode());
        po.setDesc(req.getDesc());
        po.setModifyName(userName);
        po.setModifyTime(LocalDateTime.now());
        priceAdjustRoleRelateMapper.updateById(po);
        return Boolean.TRUE;
    }

    @Override
    public String addApprover(AddOrEditApproverReq req, String userName) {
        PriceAdjustRoleRelatePO po = new PriceAdjustRoleRelatePO();
        String znSequence = sequenceUtil.getZNSequence();
        po.setCode(znSequence);
        po.setName(req.getName());
        po.setScopeName(req.getScopeName());
        po.setCompanyCode(req.getCompanyCode());
        po.setDesc(req.getDesc());
        po.setCreateName(userName);
        po.setModifyName(userName);
        po.setCreateTime(LocalDateTime.now());
        po.setModifyTime(LocalDateTime.now());
        po.setType(RoleTypeEnum.APPROVER.getValue());
        priceAdjustRoleRelateMapper.insert(po);
        return znSequence;
    }

    @Override
    public Boolean updateApprover(AddOrEditApproverReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustRoleRelatePO> wrapper = new LambdaQueryWrapper<PriceAdjustRoleRelatePO>().eq(PriceAdjustRoleRelatePO::getCode, req.getCode());
        PriceAdjustRoleRelatePO po = priceAdjustRoleRelateMapper.selectOne(wrapper);
        if (po == null){
            throw new RuntimeException("审批人配置不存在");
        }
        po.setName(req.getName());
        po.setScopeName(req.getScopeName());
        po.setCompanyCode(req.getCompanyCode());
        po.setDesc(req.getDesc());
        po.setModifyName(userName);
        po.setModifyTime(LocalDateTime.now());
        priceAdjustRoleRelateMapper.updateById(po);
        return Boolean.TRUE;
    }

    @Override
    public PageDTO<ApproverPageResp> approverPage(ApproverPageReq req) {
        LambdaQueryWrapper<PriceAdjustRoleRelatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StrUtil.isNotEmpty(req.getScopeName()),PriceAdjustRoleRelatePO::getScopeName, req.getScopeName())
                .in(CollUtil.isNotEmpty(req.getCompanyCodeList()),PriceAdjustRoleRelatePO::getCompanyCode, req.getCompanyCodeList())
                .like(StrUtil.isNotEmpty(req.getName()),PriceAdjustRoleRelatePO::getName, req.getName());
        queryWrapper.orderByDesc(PriceAdjustRoleRelatePO::getCode);

        IPage<PriceAdjustRoleRelatePO> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        page = priceAdjustRoleRelateMapper.selectPage(page, queryWrapper);
        return PageUtil.convertToPageDTO(page, ApproverPageResp.class);
    }

    @Override
    public ApproverDetailResp approverDetail(String code) {
        PriceAdjustRoleRelatePO po = priceAdjustRoleRelateMapper.selectOne(new LambdaQueryWrapper<PriceAdjustRoleRelatePO>().eq(PriceAdjustRoleRelatePO::getCode, code));
        if (po == null){
            return null;
        }
        ApproverDetailResp approverDetailResp = new ApproverDetailResp();
        approverDetailResp.setCode(po.getCode());
        approverDetailResp.setName(po.getName());
        approverDetailResp.setScopeName(po.getScopeName());
        approverDetailResp.setCompanyCode(po.getCompanyCode());
        approverDetailResp.setDesc(po.getDesc());
        SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getOrgListByUnitCode(po.getCompanyCode());
        if (sysOrganizationDTO != null) {
            approverDetailResp.setCompanyName(sysOrganizationDTO.getOrName());
        }
        return approverDetailResp;
    }

    @Override
    public SubmitterDetailResp submitterDetail(String code) {
        PriceAdjustRoleRelatePO po = priceAdjustRoleRelateMapper.selectOne(new LambdaQueryWrapper<PriceAdjustRoleRelatePO>().eq(PriceAdjustRoleRelatePO::getCode, code));
        if (po == null){
            return null;
        }
        SubmitterDetailResp submitterDetailResp = new SubmitterDetailResp();
        submitterDetailResp.setCode(po.getCode());
        submitterDetailResp.setName(po.getName());
        submitterDetailResp.setScopeName(po.getScopeName());
        //todo 报批人类型名称等枚举出来
        submitterDetailResp.setRoleName(po.getRoleCode());
        submitterDetailResp.setRoleCode(po.getRoleCode());
        submitterDetailResp.setDesc(po.getDesc());
        return submitterDetailResp;
    }
}

package com.yxt.lotprice.manager.impl.adjust;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustApprovalFlowDefinitionMapper;
import com.yxt.lotprice.service.adjustbase.iface.ApprovalFlowManager;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionBO;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionReqBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ApprovalFlowManagerImpl implements ApprovalFlowManager {
    @Autowired
    PriceAdjustApprovalFlowDefinitionMapper approvalFlowDefinitionMapper;

    @Override
    public PageDTO<ApprovalFlowDefinitionBO> page(ApprovalFlowDefinitionReqBO req) {
            return null;
    }
}

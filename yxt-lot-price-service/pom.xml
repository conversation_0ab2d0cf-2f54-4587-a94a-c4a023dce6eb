<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yxt-lot-price-service</artifactId>
    <name>yxt-lot-price-service</name>
    <description>yxt-lot-price-service</description>

    <parent>
        <groupId>com.yxt.lotprice</groupId>
        <artifactId>yxt-lot-price</artifactId>
        <version>${reversion}</version>
    </parent>

    <properties>
        <module.deploy.skip>true</module.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-common-lib</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt.lotprice</groupId>
            <artifactId>yxt-lot-price-common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-core-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--  redis相关依赖 -->
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-redis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yxt</groupId>
            <artifactId>yxt-redis-spring-boot-starter</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>io.lettuce</groupId>-->
        <!--            <artifactId>lettuce-core</artifactId>-->
        <!--            <scope>provided</scope>-->
        <!--        </dependency>-->



        <!--rocket依赖-->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!--kafka依赖-->
        <!--
                <dependency>
                    <groupId>org.springframework.kafka</groupId>
                    <artifactId>spring-kafka</artifactId>
                </dependency>
        -->
        <dependency>
            <groupId>com.yxt.org</groupId>
            <artifactId>yxt-org-read-open-sdk</artifactId>
        </dependency>
    </dependencies>

</project>

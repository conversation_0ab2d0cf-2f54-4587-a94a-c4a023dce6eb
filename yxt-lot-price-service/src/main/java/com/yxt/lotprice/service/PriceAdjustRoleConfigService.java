package com.yxt.lotprice.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
public interface PriceAdjustRoleConfigService {

    PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req);

    PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req);

    Boolean addSubmitter(AddOrEditSubmitterReq req,String userName);

    Boolean addApprover(AddOrEditApproverReq req, String userName);

    PageDTO<ApproverPageResp> approverPage(ApproverPageReq req);

    ApproverDetailResp approverDetail(String code);

    SubmitterDetailResp submitterDetail(String code);
}

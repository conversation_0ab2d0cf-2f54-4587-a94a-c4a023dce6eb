package com.yxt.lotprice.service.adjustbase;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowPageResp;
import org.springframework.web.bind.annotation.RequestBody;

public interface ApprovalFlowService {

    PageDTO<ApprovalFlowPageResp> page(@RequestBody ApprovalFlowPageReq req);

}

package com.yxt.lotprice.service.adjustbase;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.PriceAdjustRangePageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.SubmitterPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.PriceAdjustRangePageResp;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.SubmitterPageResp;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
public interface PriceAdjustRoleConfigService {

    PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req);

    PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req);

    Boolean addSubmitter(AddOrEditSubmitterReq req,String userName);

    Boolean addApprover(AddOrEditApproverReq req, String userName);
}

package com.yxt.lotprice.service.adjustbase.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.DemoBReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.DemoBResp;
import com.yxt.lotprice.service.adjustbase.model.dto.third.request.DemoThirdReq;
import com.yxt.lotprice.service.adjustbase.model.dto.third.response.DemoThirdResp;

import java.util.List;

public interface DemoChonicStoreManager {
    PageDTO<DemoThirdResp> page(DemoThirdReq req);

    List<DemoBResp> list(DemoBReq req);
}

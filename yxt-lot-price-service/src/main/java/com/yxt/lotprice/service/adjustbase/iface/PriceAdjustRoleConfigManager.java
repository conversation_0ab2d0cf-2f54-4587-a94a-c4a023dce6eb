package com.yxt.lotprice.service.adjustbase.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.PriceAdjustRangePageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.PriceAdjustRangePageResp;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
public interface PriceAdjustRoleConfigManager {

    PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req);
}

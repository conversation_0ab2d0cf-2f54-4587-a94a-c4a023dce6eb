package com.yxt.lotprice.service.adjustbase.iface;

import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditSubmitterReq;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:53
 */
public interface PriceAdjustRoleItemRelateManager {

    Boolean addSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean updateSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean addApproverItem(AddOrEditApproverReq req, String userName);

    Boolean updateApproverItem(AddOrEditApproverReq req, String userName);
}

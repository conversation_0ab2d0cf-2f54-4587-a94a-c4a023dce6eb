package com.yxt.lotprice.service.adjustbase.iface;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.SubmitterPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.SubmitterPageResp;

/**
 * <AUTHOR>
 * @date 2025/5/19 17:25
 */
public interface PriceAdjustRoleRelateManager {
    PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req);

    String addSubmitter(AddOrEditSubmitterReq req,String userName);

    Boolean updateSubmitter(AddOrEditSubmitterReq req,String userName);

    String addApprover(AddOrEditApproverReq req, String userName);

    Boolean updateApprover(AddOrEditApproverReq req, String userName);
}

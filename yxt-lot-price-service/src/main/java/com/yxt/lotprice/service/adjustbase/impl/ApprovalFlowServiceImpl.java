package com.yxt.lotprice.service.adjustbase.impl;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.ApprovalFlowService;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.ApprovalFlowPageResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ApprovalFlowServiceImpl implements ApprovalFlowService {

    @Override
    public PageDTO<ApprovalFlowPageResp> page(ApprovalFlowPageReq req) {
        return null;
    }
}

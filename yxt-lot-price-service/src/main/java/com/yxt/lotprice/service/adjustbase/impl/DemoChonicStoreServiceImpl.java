package com.yxt.lotprice.service.adjustbase.impl;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.DemoChoincStoreService;
import com.yxt.lotprice.service.adjustbase.iface.DemoChonicStoreManager;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.DemoBReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.DemoBResp;
import com.yxt.lotprice.service.adjustbase.model.dto.third.request.DemoThirdReq;
import com.yxt.lotprice.service.adjustbase.model.dto.third.response.DemoThirdResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DemoChonicStoreServiceImpl implements DemoChoincStoreService {

    @Autowired
    DemoChonicStoreManager manager;

    /**
     *
     * @param req
     * @return
     */
    @Override
    public PageDTO<DemoThirdResp> page(DemoThirdReq req) {
        return manager.page(req);
    }

    @Override
    public List<DemoBResp> list(DemoBReq req) {
        return  manager.list(req);
    }
}

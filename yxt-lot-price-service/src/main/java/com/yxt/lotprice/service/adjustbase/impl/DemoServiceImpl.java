package com.yxt.lotprice.service.adjustbase.impl;

import com.yxt.lotprice.service.adjustbase.DemoService;
import com.yxt.lotprice.service.adjustbase.model.bo.DemoBO;
import com.yxt.lotprice.service.adjustbase.iface.DemoManager;
import com.yxt.lotprice.service.adjustbase.model.dto.c.response.DemoCResp;
import com.yxt.lotprice.service.utils.BeanUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DemoServiceImpl implements DemoService {
    @Resource
    private DemoManager manager;


    @Override
    public DemoCResp selectDictByIdForC(Integer id) {
        DemoBO demoBO = manager.selectDictById(id);
        DemoCResp demoCResp = BeanUtil.copyProperties(demoBO, DemoCResp.class);
        return demoCResp;
    }

    @Override
    public DemoBO selectDictByIdFromRedis(Integer diseaseDictId) {
        return manager.selectDictByIdFromRedis(diseaseDictId);
    }
}

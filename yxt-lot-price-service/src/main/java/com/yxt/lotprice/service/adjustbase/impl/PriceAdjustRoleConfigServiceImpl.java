package com.yxt.lotprice.service.adjustbase.impl;

import cn.hutool.core.util.StrUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.adjustbase.iface.PriceAdjustRoleConfigManager;
import com.yxt.lotprice.service.adjustbase.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.adjustbase.iface.PriceAdjustRoleRelateManager;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.PriceAdjustRangePageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.SubmitterPageReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.PriceAdjustRangePageResp;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.SubmitterPageResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
@Service
@Slf4j
public class PriceAdjustRoleConfigServiceImpl implements PriceAdjustRoleConfigService {

    @Resource
    private PriceAdjustRoleConfigManager priceAdjustRoleConfigManager;

    @Resource
    private PriceAdjustRoleRelateManager priceAdjustRoleRelateManager;

    @Resource
    private PriceAdjustRoleItemRelateManager priceAdjustRoleItemRelateManager;
    @Override
    public PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req) {

        return priceAdjustRoleConfigManager.priceAdjustRangePage(req);
    }
    @Override
    public PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req) {
        return priceAdjustRoleRelateManager.submitterPage(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addSubmitter(AddOrEditSubmitterReq req,String userName) {
        //新增报批人配置
        if (StrUtil.isEmpty(req.getCode())){
            String code = priceAdjustRoleRelateManager.addSubmitter(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addSubmitterItem(req, userName);
        }else {
            //修改报批人配置
            priceAdjustRoleRelateManager.updateSubmitter(req, userName);
            priceAdjustRoleItemRelateManager.updateSubmitterItem(req, userName);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addApprover(AddOrEditApproverReq req, String userName) {
        if (StrUtil.isEmpty(req.getCode())){
            //新增审批人配置
            String code = priceAdjustRoleRelateManager.addApprover(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addApproverItem(req, userName);
        }else {
            //修改审批人配置
            priceAdjustRoleRelateManager.updateApprover(req, userName);
            priceAdjustRoleItemRelateManager.updateApproverItem(req, userName);
        }
        return Boolean.TRUE;
    }
}

package com.yxt.lotprice.service.adjustbase.model.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 调价审批流配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class ApprovalFlowDefinitionBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 流程名称
     */
    private String name;

    /**
     * 项目属性-子公司/ 集团
     */
    private String scope;

    /**
     * 子公司编码
     */
    private String companyCode;

    /**
     * 定价/调价
     */
    private String dimension;

    /**
     * 短流程/长流程
     */
    private String flowType;

    /**
     * 1-启用；0-禁用
     */
    private Integer status;

    /**
     * 节点配置，json字符串，审核流程节点以及对应的人的角色
     */
    private String nodeConfig;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 报批人角色
     */

    private String  submiterRoleCode;

}

package com.yxt.lotprice.service.adjustbase.model.dto.b.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:13
 */
@Data
@ApiModel("报批人新增/修改参数")
public class AddOrEditApproverReq {
    @ApiModelProperty("配置编码 新增为null 编辑必传")
    private String code;
    @ApiModelProperty("配置名称")
    private String name;
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("分公司编码")
    private String companyCode ;
    @ApiModelProperty("备注")
    private String desc;

    @ApiModelProperty("审批人配置")
    private List<ItemRelateApproverReq> approverList;
}

package com.yxt.lotprice.service.adjustbase.model.dto.b.request;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:10
 */
@Data
@ApiModel("条价范围配置分页查询参数")
public class PriceAdjustRangePageReq extends PageBase {
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("所属子公司/集团编码")
    private String companyCode;
    @ApiModelProperty("定价维度:STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司")
    private String dimension;
    @ApiModelProperty("报批人类型")
    private String roleCode;
    @ApiModelProperty("价格类型：RETAIL-零售价,VIP-会员价,CHRONIC-慢病价")
    private String priceType;
}

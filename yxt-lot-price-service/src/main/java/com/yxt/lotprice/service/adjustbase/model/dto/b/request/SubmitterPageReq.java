package com.yxt.lotprice.service.adjustbase.model.dto.b.request;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/19 17:13
 */
@Data
@ApiModel("报批人分页查询参数")
public class SubmitterPageReq extends PageBase {
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("报批人类型")
    private String roleCode;
    @ApiModelProperty("配置名称")
    private String name;
}

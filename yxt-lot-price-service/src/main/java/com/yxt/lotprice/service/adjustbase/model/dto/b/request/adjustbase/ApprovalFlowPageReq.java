package com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase;


import com.yxt.lang.dto.PageBase;
import com.yxt.lotprice.common.enums.adjustbase.AdjustDimensionType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustFlowType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustRoleType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustScopeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("审批流分页查询请求对象")
public class ApprovalFlowPageReq extends PageBase {

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("审批流项目属性-集团/子公司")
    private AdjustScopeType scope;

    @ApiModelProperty("提交人角色编码")
    private AdjustRoleType submiterRoleCode;

    @ApiModelProperty("所属公司编码")
    private String companyCode;

    @ApiModelProperty("维度")
    private AdjustDimensionType dimension;

    @ApiModelProperty("审批流类型")
    private AdjustFlowType flowType;

    @ApiModelProperty("审批流名称")
    private String name;
}

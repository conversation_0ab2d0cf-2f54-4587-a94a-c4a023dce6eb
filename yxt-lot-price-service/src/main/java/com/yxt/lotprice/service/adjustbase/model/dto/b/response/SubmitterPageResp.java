package com.yxt.lotprice.service.adjustbase.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/19 17:13
 */
@Data
@ApiModel("报批人分页查询结果")
public class SubmitterPageResp {

    @ApiModelProperty("配置名称")
    private String name;

    @ApiModelProperty("配置编码")
    private String code;

    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;

    @ApiModelProperty("备注")
    private String desc;

    @ApiModelProperty("报批人类别名称")
    private String roleName;

    @ApiModelProperty("报批人类别编码")
    private String roleCode;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;

    @ApiModelProperty("修改人")
    private String modifyName;
}

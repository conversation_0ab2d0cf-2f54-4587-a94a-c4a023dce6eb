package com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase;


import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("审批流分页查询请求对象")
public class ApprovalFlowPageResp{

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("审批流名称")
    private String name;

    @ApiModelProperty("审批流项目属性-集团/子公司")
    private String scope;

    @ApiModelProperty("所属公司编码")
    private String companyCode;

    @ApiModelProperty("所属公司名称")
    private String companyName;

    @ApiModelProperty("报批人角色编码")
    private String submiterRoleCode;

    @ApiModelProperty("报批人角色名称")
    private String submiterRoleName;

    @ApiModelProperty("定/调价类别-定价/调价")
    private String dimension;

    @ApiModelProperty("审批流流程类型")
    private String flowType;

    @ApiModelProperty("修改人")
    private LocalDateTime modifyName;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;

}

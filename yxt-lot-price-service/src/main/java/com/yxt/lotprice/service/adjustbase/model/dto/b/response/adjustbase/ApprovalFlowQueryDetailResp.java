package com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase;


import com.yxt.lang.dto.PageBase;
import com.yxt.lotprice.common.enums.adjustbase.AdjustDimensionType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustFlowType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustRoleType;
import com.yxt.lotprice.common.enums.adjustbase.AdjustScopeType;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.ApprovalFlowSaveDetailReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("审批流分页查询请求对象")
public class ApprovalFlowQueryDetailResp{

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("审批流项目属性-集团/子公司")
    private AdjustScopeType scope;

    @ApiModelProperty("提交人角色编码")
    private AdjustRoleType submiterRoleCode;

    @ApiModelProperty("所属公司编码")
    private String companyCode;

    @ApiModelProperty("维度")
    private AdjustDimensionType dimension;

    @ApiModelProperty("审批流类型")
    private AdjustFlowType flowType;

    @ApiModelProperty("审批流名称")
    private String name;

    @ApiModelProperty("审批流节点列表")
    private List<ApprovalFlowSaveDetailReq.ApprovalFlowNodeItem> configNodeList;

    @Data
    @ApiModel("审批流节点配置信息")
    public static class ApprovalFlowNodeItem{
        @ApiModelProperty("序号")
        private String no;

        @ApiModelProperty("id")
        private Long id;

        @ApiModelProperty("id")
        private String name;

        @ApiModelProperty("审批人职能类别")
        private AdjustRoleType roleCode;

        @ApiModelProperty("备注")
        private String desc;
    }
}

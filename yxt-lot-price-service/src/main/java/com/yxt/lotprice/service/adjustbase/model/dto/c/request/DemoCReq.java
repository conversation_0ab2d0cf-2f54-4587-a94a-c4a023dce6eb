package com.yxt.lotprice.service.adjustbase.model.dto.c.request;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class DemoCReq extends MiddleRequestBase {
    @ApiModelProperty("请求编号")
    private @NotBlank(
            message = "请求编号不能为空"
    ) String requestId;

    @ApiModelProperty("词典id")
    private @NotNull(
            message = "词典id不能为空"
    ) Integer diseaseDictId;
}

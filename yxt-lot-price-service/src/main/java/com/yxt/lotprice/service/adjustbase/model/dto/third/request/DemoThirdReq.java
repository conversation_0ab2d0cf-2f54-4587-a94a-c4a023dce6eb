package com.yxt.lotprice.service.adjustbase.model.dto.third.request;

import com.yxt.lang.dto.api.MiddleRequestBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class DemoThirdReq extends MiddleRequestBase {
    @ApiModelProperty("请求编号")
    private @NotBlank(
            message = "请求编号不能为空"
    ) String requestId;

    @ApiModelProperty("最大慢病会员")
    private @NotNull(
            message = "最大慢病会员数不能为空"
    ) Integer maxMemberAmount;

    private Integer page;

    private Integer size;
}

package com.yxt.lotprice.service.adjustbase.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/19 15:13
 */
@Getter
@AllArgsConstructor
public enum DimensionEnum {
    STORE("STORE", "门店"),
    PRICE_GROUP("PRICE_GROUP", "价格组"),
    COMPANY("COMPANY", "子公司"),
    ;

    private String value;
    private String name;

    public static DimensionEnum getByValue(String value) {
        for (DimensionEnum item : values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

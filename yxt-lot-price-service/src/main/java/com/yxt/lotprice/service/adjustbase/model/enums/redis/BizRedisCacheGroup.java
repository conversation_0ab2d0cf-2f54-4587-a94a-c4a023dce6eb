package com.yxt.lotprice.service.adjustbase.model.enums.redis;

/**
 * 缓存组名称常量
 * <p>
 * key 格式为 cacheName#ttl#append
 * <p>
 * cacheName key业务分组
 * <p>
 * ttl 过期时间 秒 如果设置为0则不过期 默认为0, 不填写不过期
 * <p>
 * append 是否拼接全局前缀，true/false 不填写默认拼接【默认取系统名， yml cache.prefix 优先级高于默认系统名】
 * <p>
 * 例子:          test#60#false
 * redis真正key   test:{业务key}  60失效
 */
public interface BizRedisCacheGroup {
    /**
     * 演示案例   redis中保存的最终key名称   TEST:{业务key}
     * TEST:       缓存key前缀
     * 60:         失效时间 60秒
     * false:      不拼接系统前缀
     */
    String TEST = "TEST#60#false";

    /**
     * 演示案例   redis中保存的最终key名称   TEST:{业务key}
     * TEST1:      缓存key前缀
     * 60:         失效时间 60秒
     * true:       拼接系统前缀
     */
    String TEST1 = "TEST1#60#true";

    /**
     * 演示案例   redis中保存的最终key名称   全局前缀:CHANNEL_RELATION_TEST:{业务key}
     * cacheName:   缓存key前缀
     * 60:          失效时间 60秒
     */
    String CHANNEL_RELATION_TEST = "CHANNEL_RELATION_TEST#60";

    /**
     * 演示案例   redis中保存的最终key名称   CHANNEL_RELATION_TEST:{业务key}
     * cacheName:   缓存key前缀
     * false:      不拼接系统前缀
     */
    String CHANNEL_RELATION_TEST_001 = "CHANNEL_RELATION_TEST_001#false";
}

package com.yxt.lotprice.service.adjustbase.model.enums.redis;

import com.yxt.redis.model.RedisKeyGroup;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * RedisClient工具类中包装了操作redis的基本方法，具体可见类中的注释
 * <p>
 * 使用RedisClient操作redis时必须实现RedisKeyGroup接口，并且返回group，用于组成完整的key前缀  {appname:gourp:}
 * 例如药师云(yxt-medical-prescription)保存用户ID为1的用户信息到redis中
 * <p>
 * redisClient.set(RedisKeyEnum.CHANNEL_RELATION_LIST, "key001", 60, TimeUnit.SECONDS)
 * 最终key为yxt-medical-prescription:CHANNEL_RELATION_LIST:key001
 * <p>
 * 如果配置了yml 配置了全局前缀
 * 举例 cache.prefix；xxxSystem
 * 最终key为xxxSystem:CHANNEL_RELATION_LIST:key001
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum BizRedisKeyGroup implements RedisKeyGroup {

    /**
     *
     */
    CHANNEL_RELATION_TEST(true, "渠道关联"),
    CHANNEL_RELATION_LIST(false, "渠道关联LIST");

    /**
     * 是否拼接系统名称前缀
     */
    private Boolean fixSystem;

    /**
     * 描述
     */
    private String desc;


    @Override
    public String getGroup() {
        return name();
    }

    @Override
    public Boolean fixSystem() {
        return fixSystem;
    }

}

package com.yxt.lotprice.service.cache;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ListenableFuture;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.service.constant.LocalConstants;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationUnitTreeDTO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;



@Service
public class BaseInfoCacheService implements ApplicationRunner {

    @Autowired
    BaseInfoClientAdapter baseInfoClientAdapter;

    @Value("${org.unit.root.orgid:10010000}")
    String rootId;

    //所有机构
    private static final String ALL_ORG = "ALL_ORG";

    //业务单元树
    private static final String UNIT_ORG = "UNIT_ORG";

    private final LoadingCache<String, List<SysOrganizationDTO>> orgCache = CacheBuilder
            .newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .refreshAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<String, List<SysOrganizationDTO>>() {
                @Override
                public List<SysOrganizationDTO> load(String key) {
                    return loadAllOrgList(key);
                }

                @Override
                public ListenableFuture<List<SysOrganizationDTO>> reload(String key, List<SysOrganizationDTO> oldValue) throws Exception {
                    return super.reload(key, oldValue);
                }
            });

    private final LoadingCache<String, SysOrganizationUnitTreeDTO> unitTreeCache = CacheBuilder
            .newBuilder()
            .expireAfterWrite(7, TimeUnit.HOURS)
            .refreshAfterWrite(4, TimeUnit.HOURS)
            .build(new CacheLoader<String, SysOrganizationUnitTreeDTO>() {
                @Override
                public SysOrganizationUnitTreeDTO load(String key) throws ExecutionException {
                    return loadWholeUnitTree(key);
                }

                @Override
                public ListenableFuture<SysOrganizationUnitTreeDTO> reload(String key, SysOrganizationUnitTreeDTO oldValue) throws Exception {
                    return super.reload(key, oldValue);
                }
            });

    @SneakyThrows
    public List<SysOrganizationDTO> getOrgList() {
            return orgCache.get(ALL_ORG);
    }

    @SneakyThrows
    public SysOrganizationUnitTreeDTO getUnitTree(String unitCode) {
        SysOrganizationUnitTreeDTO wholeTree = unitTreeCache.get(UNIT_ORG);
        return bfsSearch(wholeTree, unitCode);
    }

    private List<SysOrganizationDTO> loadAllOrgList(String key) {
        if (ALL_ORG.equals(key)) {
            try {
                ResponseBase<List<SysOrganizationDTO>> result = baseInfoClientAdapter.getAllOrgByMerCode(LocalConstants.MER_CODE_500001);
                if (result == null || !result.checkSuccess()) {
                    throw new YxtBizException("缓存全量组织机构信息查询时报错保存！");
                } else {
                    return result.getData();
                }
            } catch (Exception e) {
                ExLogger.logger("getNoneNullOrgList").error("缓存全量组织机构信息查询时报错保存!", e);
            }
            return null;
        }else {
            return Lists.newArrayList();
        }
    }

    private SysOrganizationUnitTreeDTO loadWholeUnitTree(String key) throws ExecutionException {
        if (UNIT_ORG.equals(key)) {
            List<SysOrganizationDTO> orgList = getOrgList();
            if (orgList != null) {
                List<SysOrganizationDTO> unitList = orgList.stream().filter(x ->
                                (StringUtils.isNotBlank(x.getLayer()) && ("1".equals(x.getLayer()) || "2".equals(x.getLayer()) || "3".equals(x.getLayer()))
                                        || rootId.equals(x.getId())))
                        .collect(Collectors.toList());

                SysOrganizationDTO root = unitList.stream().filter(x -> rootId.equals(x.getId())).findFirst().orElse(null);
                if (root == null) {
                    return null;
                } else {
                    SysOrganizationUnitTreeDTO treeRoot = BeanUtil.copyProperties(root, SysOrganizationUnitTreeDTO.class);
                    List<SysOrganizationUnitTreeDTO> level1 = unitList.stream().filter(x -> ("1".equals(x.getLayer()))).map(x -> BeanUtil.copyProperties(x, SysOrganizationUnitTreeDTO.class)).collect(Collectors.toList());
                    treeRoot.setChild(level1);
                    level1.forEach(lvl1 -> {
                        List<SysOrganizationUnitTreeDTO> lv2List = unitList.stream().filter(x -> "2".equals(x.getLayer()) && x.getParentPath().startsWith(lvl1.getParentPath())).map(x -> BeanUtil.copyProperties(x, SysOrganizationUnitTreeDTO.class)).collect(Collectors.toList());
                        lvl1.setChild(lv2List);
                        lv2List.forEach(lvl2 -> {
                            List<SysOrganizationUnitTreeDTO> lv3List = unitList.stream().filter(x -> "3".equals(x.getLayer()) && x.getParentPath().startsWith(lvl2.getParentPath())).map(x -> BeanUtil.copyProperties(x, SysOrganizationUnitTreeDTO.class)).collect(Collectors.toList());
                            lvl2.setChild(lv3List);
                        });

                    });
                    return treeRoot;
                }
            } else {
                return null;
            }
        }else {
            return null;
        }
    }

    private SysOrganizationUnitTreeDTO bfsSearch(SysOrganizationUnitTreeDTO root, String unitCode){
        if (root == null) {
            return null;
        }
        if (StringUtils.isBlank(unitCode)){
            return root;
        }
        Queue<SysOrganizationUnitTreeDTO> queue = new LinkedList<>();
        queue.offer(root);

        while (!queue.isEmpty()) {
            SysOrganizationUnitTreeDTO node = queue.poll();
            if (node != null){
                if (StringUtils.equals(unitCode, node.getUnitCode())) {
                    return node;
                }
                if (node.getChild() != null) {
                    for (SysOrganizationUnitTreeDTO child : node.getChild()) {
                        queue.offer(child);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 服务启动成功后，就调会员的接口去查门店列表并缓存
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args){
        try {
            getOrgList();
            getUnitTree(null);
        }catch (Exception e){
            ExLogger.logger("BaseInfoCacheService").error("服务启动时，缓存机构信息树失败，原因：%s", e.getMessage(), e);
        }

    }

}

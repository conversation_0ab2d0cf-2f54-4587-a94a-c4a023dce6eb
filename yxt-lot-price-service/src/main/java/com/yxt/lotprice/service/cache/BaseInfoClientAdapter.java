package com.yxt.lotprice.service.cache;


import cn.hutool.core.collection.CollUtil;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.constant.LocalConstants;
import com.yxt.lotprice.service.model.dto.third.response.QuerySubComInfoReqDTO;
import com.yxt.lotprice.service.model.dto.third.response.QuerySubComResp;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.utils.BeanUtil;
import com.yxt.lotprice.service.utils.MdmAdapterUtil;
import com.yxt.org.read.opensdk.org.dto.request.old.GetAllOrgByMerCodeReqDTO;
import com.yxt.org.read.opensdk.org.dto.request.old.QuerySubComInfoDTO;
import com.yxt.org.read.opensdk.org.dto.response.old.QuerySubCompResDTO;
import com.yxt.org.read.opensdk.org.dto.response.old.SysOrganization;
import com.yxt.org.read.opensdk.org.service.OldOrgQueryOpenApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BaseInfoClientAdapter {


    @Autowired
    private OldOrgQueryOpenApi yxtOrgOrgQueryOpenApi;

    public ResponseBase<List<SysOrganizationDTO>> getAllOrgByMerCode(String merCode){
        ResponseBase<List<SysOrganizationDTO>> response = new ResponseBase<>();
        GetAllOrgByMerCodeReqDTO reqDTO = new GetAllOrgByMerCodeReqDTO();
        reqDTO.setMerCode(merCode);
        com.yxt.lang.dto.api.ResponseBase<List<SysOrganization>> responseBase = yxtOrgOrgQueryOpenApi.getAllOrgByMerCode(reqDTO);
        MdmAdapterUtil.copyResponseBase(responseBase, response);
        response.setData(BeanUtil.copyList(responseBase.getData(), SysOrganizationDTO.class));
        return response;
    }

    public ResponseBase<PageDTO<QuerySubComResp>> querySubCompInfo(QuerySubComInfoReqDTO dto) {
        ResponseBase<PageDTO<QuerySubComResp>> response = new ResponseBase<>();
        PageDTO<QuerySubComResp> page = new PageDTO<>();
        response.setData(page);
        QuerySubComInfoDTO queryDTO = new QuerySubComInfoDTO();
        // 这个拷贝 属性不同类型 会拷贝失败
        BeanUtil.copyProperties(dto, queryDTO);
        queryDTO.setCurrentPage(dto.getCurrentPage());
        queryDTO.setPageSize(dto.getPageSize());
        queryDTO.setMerCode(LocalConstants.MER_CODE_500001);
        com.yxt.lang.dto.api.ResponseBase<com.yxt.lang.dto.api.PageDTO<QuerySubCompResDTO>> responseBase = yxtOrgOrgQueryOpenApi.querySubCompInfo(queryDTO);
        MdmAdapterUtil.copyResponseBase(responseBase, response);
        com.yxt.lang.dto.api.PageDTO<QuerySubCompResDTO> pageDTO = responseBase.getData();
        if (CollUtil.isNotEmpty(pageDTO.getData())) {
            MdmAdapterUtil.copyPageDTO(pageDTO, page);
            page.setData(BeanUtil.copyList(pageDTO.getData(), QuerySubComResp.class));
        }

        return response;
    }
}

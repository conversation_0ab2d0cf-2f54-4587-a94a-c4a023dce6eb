package com.yxt.lotprice.service.cache;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationUnitTreeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
public class OrgInfoCacheUtils {

    public static List<SysOrganizationDTO> getStoreListByUnitCode(String unitCode) {
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            SysOrganizationDTO parent = orgList.stream().filter(x -> StringUtils.equals(unitCode, x.getUnitCode())).findFirst().orElse(null);
            if (parent != null) {
                return orgList.stream().filter(x -> {
                    //以当前节点为父节点，layer=4,的所有节点
                    return StringUtils.isNotBlank(x.getParentPath())
                            && x.getParentPath().startsWith(parent.getParentPath())
                            && "4".equals(x.getLayer());
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("getStoreListByUnitCode，查询机构下店铺信息失败", e);
        }
        return new ArrayList<>();
    }
    public static List<SysOrganizationUnitTreeDTO> getExistUnitCodes(List<String> unitCodes){
        return unitCodes.stream().map(OrgInfoCacheUtils::getUnitTree).filter(Objects::nonNull).collect(Collectors.toList());
    }


    public static SysOrganizationUnitTreeDTO getUnitTree(String unitCode){
        try {
            return SpringUtil.getBean(BaseInfoCacheService.class).getUnitTree(unitCode);
        }catch (Exception e){
            log.error("getStoreListByUnitCode，查询业务单元节点失败", e);
        }
        return null;
    }

    public static List<SysOrganizationDTO> getStoreListByUnitCodes(List<String> unitCodes) {
        List<SysOrganizationDTO> orgList = new ArrayList<>();
        for (String unitCode : unitCodes) {
            orgList.addAll(getStoreListByUnitCode(unitCode));
        }
        return orgList;
    }

    public static List<SysOrganizationDTO> getChildCompany() {
        List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
        if (orgList != null){
            return orgList.stream().filter(x->"1".equals(x.getLayer()) && StringUtils.isNotBlank(x.getUnitCode()) && x.getUnitCode().length() < 6).collect(Collectors.toList());
        }else {
            return Lists.newArrayList();
        }
    }

    public static Map<String, String> getChildCompanyMap() {
        try {
            return getChildCompany().stream().collect(Collectors.toMap(SysOrganizationDTO::getUnitCode, SysOrganizationDTO::getUnitName, (v1,v2)->v1));
        }catch (Exception e){
            log.error("getChildCompanyMap出错了！"+e.getMessage(), e);
        }
        return Maps.newHashMap();
    }

    public static List<SysOrganizationDTO> getStoreListByIds(Set<String> storeIdSet){
        if (CollectionUtils.isEmpty(storeIdSet)){
            return Lists.newArrayList();
        }else {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            if (CollectionUtils.isEmpty(orgList)){
                return Lists.newArrayList();
            }else {
                return orgList.stream().filter(x->storeIdSet.contains(x.getId())).collect(Collectors.toList());
            }
        }
    }

    public static List<SysOrganizationDTO> getStoreListByCodes(Set<String> storeCodeSet){
        if (CollectionUtils.isEmpty(storeCodeSet)){
            return Lists.newArrayList();
        }else {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            if (CollectionUtils.isEmpty(orgList)){
                return Lists.newArrayList();
            }else {
                return orgList.stream().filter(x->storeCodeSet.contains(x.getOrCode())).collect(Collectors.toList());
            }
        }
    }

    public static SysOrganizationDTO getStoreListByCode(String storeCode){
        if (StrUtil.isEmpty(storeCode)){
            return null;
        }else {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            if (CollectionUtils.isEmpty(orgList)){
                return null;
            }else {
                return orgList.stream().filter(x->storeCode.equals(x.getOrCode())).findFirst().orElse(null);
            }
        }
    }

    /**
     * 通过机构id获取门店（支持通过门店机构id获取）
     *
     * @param orgId 机构id
     * @return 门店集合
     */
    public static List<SysOrganizationDTO> getStoreListByOrgId(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            return Collections.emptyList();
        }
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            return orgList.stream().filter(f -> f.getParentPath().startsWith(orgId) && StringUtils.isNotEmpty(f.getOrCode())
                    && ("4".equals(f.getLayer()) || Integer.valueOf(3).equals(f.getOrClass()))).collect(Collectors.toList());
        } catch (Exception e) {
            ExLogger.logger("getStoreListByOrgId").info("获取单机构下店铺异常", e);
        }
        return new ArrayList<>();
    }

    /**
     * 通过多个机构id获取门店（支持通过门店机构id获取），取多个机构的并集
     *
     * @param orgIds 机构ids
     * @return 门店集合，根据orCode 去重
     */
    public static List<SysOrganizationDTO> getStoreListByOrgIds(Collection<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        List<SysOrganizationDTO> storeList = new ArrayList<>();
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            Set<String> orCodeSet = new HashSet<>();
            orgIds.forEach(orgId -> {
                String parentPath = orgList.stream().filter(f -> orgId.equals(f.getId())).findFirst().map(SysOrganizationDTO::getParentPath).orElse("");
                orgList.stream()
                        .filter(f -> Objects.nonNull(f.getParentPath()) && f.getParentPath().startsWith(parentPath) && StringUtils.isNotEmpty(f.getOrCode()) && StringUtils.isNotEmpty(f.getId())
                                && ("4".equals(f.getLayer()) || Integer.valueOf(3).equals(f.getOrClass()))
                                && orCodeSet.add(f.getOrCode()))
                        .forEach(storeList::add);
            });
            orCodeSet.clear();
        } catch (Exception e) {
            ExLogger.logger("getStoreListByOrgId").info("获取多机构下店铺异常", e);
        }
        return storeList;
    }

    /**
     * 获取层级为1的机构
     * @param orgIds
     * @return
     */
    public static List<SysOrganizationDTO> getStoreListByOrgIdsWithLayer1(Collection<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        List<SysOrganizationDTO> storeList = new ArrayList<>();
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            Set<String> orCodeSet = new HashSet<>();
            orgIds.forEach(orgId -> {
                String parentPath = orgList.stream().filter(f -> orgId.equals(f.getId())).findFirst().map(SysOrganizationDTO::getParentPath).orElse("");
                orgList.stream()
                        .filter(f -> Objects.nonNull(f.getParentPath()) && f.getParentPath().startsWith(parentPath) && StringUtils.isNotEmpty(f.getOrCode()) && StringUtils.isNotEmpty(f.getId())
                                && ("1".equals(f.getLayer()))
                                && orCodeSet.add(f.getOrCode()))
                        .forEach(storeList::add);
            });
            orCodeSet.clear();
        } catch (Exception e) {
            ExLogger.logger("getStoreListByOrgId").info("获取多机构下店铺异常", e);
        }
        return storeList;
    }
    /**
     * 获取机构信息
     *
     * @param orgIds 机构ids
     * @return 机构
     */
    public static List<SysOrganizationDTO> getOrgListByOrgIds(Collection<String> orgIds) {
        if (CollectionUtils.isEmpty(orgIds)) {
            return Collections.emptyList();
        }
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            return orgList.stream().filter(f -> orgIds.contains(f.getId())).collect(Collectors.toList());
        } catch (Exception e) {
            ExLogger.logger("getOrgListByOrgIds").info("获取机构信息异常", e);
        }
        return Collections.emptyList();
    }

    public static List<SysOrganizationDTO> getOrgListByOrgCodes(Collection<String> orgCodes) {
        if (CollectionUtils.isEmpty(orgCodes)) {
            return Collections.emptyList();
        }
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            return orgList.stream().filter(f -> orgCodes.contains(f.getOrCode())).collect(Collectors.toList());
        } catch (Exception e) {
            ExLogger.logger("getOrgListByOrgCodes").info("获取机构信息异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取机构信息
     *
     * @param unitCodes 业务单元ids
     * @return 机构
     */
    public static List<SysOrganizationDTO> getOrgListByUnitCodes(Collection<String> unitCodes) {
        if (CollectionUtils.isEmpty(unitCodes)) {
            return Collections.emptyList();
        }
        try {
            Map<String, SysOrganizationDTO> orgUnitCodeMap = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList()
                    .stream().collect(Collectors.toMap(SysOrganizationDTO::getUnitCode, Function.identity(), (v1, v2) -> v1));
            return unitCodes.stream().map(orgUnitCodeMap::get).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            ExLogger.logger("getOrgListByUnitCodes").info("获取机构信息异常", e);
        }
        return Collections.emptyList();
    }

    public static SysOrganizationDTO getOrgListByUnitCode(String unitCode) {
        if (StrUtil.isEmpty(unitCode)) {
            return null;
        }
        try {
            Map<String, SysOrganizationDTO> orgUnitCodeMap = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList()
                    .stream().collect(Collectors.toMap(SysOrganizationDTO::getUnitCode, Function.identity(), (v1, v2) -> v1));
            return orgUnitCodeMap.get(unitCode);
        } catch (Exception e) {
            ExLogger.logger("getOrgListByUnitCodes").info("获取机构信息异常", e);
        }
        return null;
    }

    public static SysOrganizationDTO getBranchByUnitCode(String unitCode) {
        if (StrUtil.isEmpty(unitCode)) {
            return null;
        }
        try {
            Map<String, SysOrganizationDTO> orgUnitCodeMap = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList()
                    .stream().collect(Collectors.toMap(SysOrganizationDTO::getUnitCode, Function.identity(), (v1, v2) -> v1));

            SysOrganizationDTO sysOrganizationDTO = orgUnitCodeMap.get(unitCode);
            while (true) {
                sysOrganizationDTO = orgUnitCodeMap.get(sysOrganizationDTO.getOrParent());
                if (sysOrganizationDTO == null) {
                    break;
                }
                if (Objects.equals(sysOrganizationDTO.getLayer(), "2")){
                    break;
                }
            }
            return sysOrganizationDTO;
        } catch (Exception e) {
            ExLogger.logger("getOrgListByUnitCodes").info("获取机构信息异常", e);
        }
        return null;
    }

    public static String getStoreStartWithUnitCodes(String storeCode) {
        try {
            List<SysOrganizationDTO> orgList = SpringUtil.getBean(BaseInfoCacheService.class).getOrgList();
            String parentPath = orgList.stream().filter(x -> x.getOrCode().equals(storeCode)).findFirst().map(SysOrganizationDTO::getParentPath).orElse("");
            if (StringUtils.isNotBlank(parentPath)){
                SysOrganizationDTO sysOrganizationDTO = orgList.stream().filter(x -> StrUtil.isNotEmpty(x.getParentPath()) && parentPath.startsWith(x.getParentPath()) && "1".equals(x.getLayer())).findFirst().orElse(null);
                if (sysOrganizationDTO != null){
                    return sysOrganizationDTO.getUnitCode();
                }
            }
        }catch (Exception e){
            log.error("getStoreStartWithUnitCodes，查询机构下店铺信息失败", e);
        }
        return null;
    }
}

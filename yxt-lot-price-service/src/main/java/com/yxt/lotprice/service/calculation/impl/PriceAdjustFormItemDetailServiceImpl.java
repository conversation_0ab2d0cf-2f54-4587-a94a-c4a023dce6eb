package com.yxt.lotprice.service.calculation.impl;

import com.yxt.lotprice.service.calculation.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/21 18:10
 * Author: qs
 */

@Service
public class PriceAdjustFormItemDetailServiceImpl implements PriceAdjustFormItemDetailService {

    @Resource
    private PriceAdjustFormItemDetailManager priceAdjustFormItemDetailManager;

    @Override
    public boolean saveFormItemDetail() {
        return false;
    }

    @Override
    public boolean deleteByStoreAndPriceGroup(String storeCode) {
        return false;
    }

    @Override
    public List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes) {
        return priceAdjustFormItemDetailManager.listEnableByStoreCodeErpCodes(priceTypeEnum, storeCode, erpCodes);
    }
}

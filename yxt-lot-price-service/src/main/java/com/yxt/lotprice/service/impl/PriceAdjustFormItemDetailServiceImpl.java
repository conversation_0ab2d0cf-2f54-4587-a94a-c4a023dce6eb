package com.yxt.lotprice.service.impl;

import com.yxt.lotprice.service.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Since: 2025/05/20 10:22
 * Author: qs
 */

@Service
public class PriceAdjustFormItemDetailServiceImpl implements PriceAdjustFormItemDetailService {


    @Resource
    private PriceAdjustFormItemDetailManager priceAdjustFormItemDetailManager;

    @Override
    public void test(List<String> compycodelist, List<String> storeIdList) {
        priceAdjustFormItemDetailManager.test(compycodelist, storeIdList);
    }
}

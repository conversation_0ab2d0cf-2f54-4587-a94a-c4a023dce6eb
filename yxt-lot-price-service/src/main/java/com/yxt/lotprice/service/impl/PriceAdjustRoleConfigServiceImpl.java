package com.yxt.lotprice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleConfigManager;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleItemRelateManager;
import com.yxt.lotprice.service.manager.iface.PriceAdjustRoleRelateManager;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:18
 */
@Service
@Slf4j
public class PriceAdjustRoleConfigServiceImpl implements PriceAdjustRoleConfigService {

    @Resource
    private PriceAdjustRoleConfigManager priceAdjustRoleConfigManager;

    @Resource
    private PriceAdjustRoleRelateManager priceAdjustRoleRelateManager;

    @Resource
    private PriceAdjustRoleItemRelateManager priceAdjustRoleItemRelateManager;
    @Override
    public PageDTO<PriceAdjustRangePageResp> priceAdjustRangePage(PriceAdjustRangePageReq req) {

        return priceAdjustRoleConfigManager.priceAdjustRangePage(req);
    }
    @Override
    public PageDTO<SubmitterPageResp> submitterPage(SubmitterPageReq req) {
        return priceAdjustRoleRelateManager.submitterPage(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addSubmitter(AddOrEditSubmitterReq req,String userName) {
        //新增报批人配置
        if (StrUtil.isEmpty(req.getCode())){
            String code = priceAdjustRoleRelateManager.addSubmitter(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addSubmitterItem(req, userName);
        }else {
            //修改报批人配置
            priceAdjustRoleRelateManager.updateSubmitter(req, userName);
            priceAdjustRoleItemRelateManager.updateSubmitterItem(req, userName);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addApprover(AddOrEditApproverReq req, String userName) {
        if (StrUtil.isEmpty(req.getCode())){
            //新增审批人配置
            String code = priceAdjustRoleRelateManager.addApprover(req, userName);
            req.setCode(code);
            priceAdjustRoleItemRelateManager.addApproverItem(req, userName);
        }else {
            //修改审批人配置
            priceAdjustRoleRelateManager.updateApprover(req, userName);
            priceAdjustRoleItemRelateManager.updateApproverItem(req, userName);
        }
        return Boolean.TRUE;
    }

    @Override
    public PageDTO<ApproverPageResp> approverPage(ApproverPageReq req) {
        PageDTO<ApproverPageResp> pageDTO = priceAdjustRoleRelateManager.approverPage(req);

        if (CollUtil.isNotEmpty(pageDTO.getData())) {
            List<ApproverPageResp> list = pageDTO.getData();
            list.forEach(item -> {
                SysOrganizationDTO sysOrganizationDTO = OrgInfoCacheUtils.getOrgListByUnitCode(item.getCompanyCode());
                if (sysOrganizationDTO != null) {
                    item.setCompanyName(sysOrganizationDTO.getOrName());
                }
            });
        }
        return pageDTO;
    }

    @Override
    public ApproverDetailResp approverDetail(String code) {
        return priceAdjustRoleRelateManager.approverDetail(code);
    }

    @Override
    public SubmitterDetailResp submitterDetail(String code) {
        //基础信息
        SubmitterDetailResp submitterDetailResp = priceAdjustRoleRelateManager.submitterDetail(code);
        //组织信息
        List<ItemRelateSubmitterResp> submitterItemList = priceAdjustRoleItemRelateManager.getSubmitterItemList(code, submitterDetailResp.getScopeName());
        submitterDetailResp.setSubmitterList(submitterItemList);
        return submitterDetailResp;
    }
}

package com.yxt.lotprice.service.manager.iface;

import com.yxt.lotprice.service.model.dto.b.request.AddOrEditApproverReq;
import com.yxt.lotprice.service.model.dto.b.request.AddOrEditSubmitterReq;
import com.yxt.lotprice.service.model.dto.b.response.ItemRelateSubmitterResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 11:53
 */
public interface PriceAdjustRoleItemRelateManager {

    Boolean addSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean updateSubmitterItem(AddOrEditSubmitterReq req, String userName);

    Boolean addApproverItem(AddOrEditApproverReq req, String userName);

    Boolean updateApproverItem(AddOrEditApproverReq req, String userName);

    List<ItemRelateSubmitterResp> getSubmitterItemList(String code,String scopeName);
}

package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 17:13
 */
@Data
@ApiModel("报批人分页查询参数")
public class ApproverPageReq extends PageBase {
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("所属子公司编码")
    private List<String> companyCodeList;
    @ApiModelProperty("配置名称")
    private String name;
}

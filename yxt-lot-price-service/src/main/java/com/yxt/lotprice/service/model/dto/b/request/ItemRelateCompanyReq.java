package com.yxt.lotprice.service.model.dto.b.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:19
 */
@Data
@ApiModel("报批人组织配置")
public class ItemRelateCompanyReq {
    @ApiModelProperty("分公司编码")
    private String companyCode;

    @ApiModelProperty("报批人就是选择的机构，审批人就是选择的员工")
    private String roleData;
}

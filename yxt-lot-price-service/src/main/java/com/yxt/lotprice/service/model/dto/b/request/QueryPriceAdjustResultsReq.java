package com.yxt.lotprice.service.model.dto.b.request;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 查询调价计算结果请求参数
 * 
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ApiModel("查询调价计算结果请求参数")
public class QueryPriceAdjustResultsReq extends PageBase {

    @ApiModelProperty("商品编码")
    private String erpCode;

    @ApiModelProperty("门店编码")
    private String storeCode;

    @ApiModelProperty("门店ID")
    private String storeId;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("价格类型")
    private String priceType;

    @ApiModelProperty("调价单编码")
    private String formNo;

    @ApiModelProperty("机构类型")
    private String orgType;

    @ApiModelProperty("价格生效开始时间-起")
    private LocalDate startTimeBegin;

    @ApiModelProperty("价格生效开始时间-止")
    private LocalDate startTimeEnd;

    @ApiModelProperty("推送POS状态")
    private String pushPosStatus;
}

package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 12:08
 */
@Data
@ApiModel("条价范围配置分页查询结果")
public class PriceAdjustRangePageResp {

    @ApiModelProperty("报批人类别名称")
    private String roleName;

    @ApiModelProperty("报批人类别编码")
    private String roleCode;

    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;

    @ApiModelProperty("所属子公司/集团编码")
    private String companyCode;

    @ApiModelProperty("所属子公司名称")
    private String companyName;

    @ApiModelProperty("定价/调价维度,STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司")
    private List<String> dimension;

    @ApiModelProperty("定价/调价类型，ADJUST-调价，PRICING-定价")
    private String priceOpsType;

    @ApiModelProperty("价格类型：RETAIL-零售价,VIP-会员价,CHRONIC-慢病价")
    private List<String> priceType;

    @ApiModelProperty("配置编码")
    private String code;
}

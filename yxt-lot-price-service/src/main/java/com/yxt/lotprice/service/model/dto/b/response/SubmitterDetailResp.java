package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:10
 */
@Data
@ApiModel("报批人配置详情结果")
public class SubmitterDetailResp {
    @ApiModelProperty("配置编码")
    private String code;
    @ApiModelProperty("配置名称")
    private String name;
    @ApiModelProperty("项目属性：GROUP-集团，COMPANY-子公司")
    private String scopeName;
    @ApiModelProperty("报批人类别名称")
    private String roleName;
    @ApiModelProperty("报批人类别编码")
    private String roleCode;
    @ApiModelProperty("备注")
    private String desc;

    @ApiModelProperty("组织配置")
    private List<ItemRelateSubmitterResp> submitterList;
}

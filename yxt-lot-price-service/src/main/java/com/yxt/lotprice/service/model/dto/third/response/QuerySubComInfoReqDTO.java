package com.yxt.lotprice.service.model.dto.third.response;

import com.yxt.lang.dto.PageBase;
import lombok.Data;

import java.util.List;

/**
 * @Author: mengzilei
 * @Date: 2024-09-03  10:52
 * 查询数仓所有下级公司、区域、部门信息
 */


@Data
public class QuerySubComInfoReqDTO extends PageBase {

    /**
     * 组织erpCodeList（不传此参数时，查询所有机构类型数据）
     */
    private List<String> erpCodeList;

    /**
     * 机构类型（1：公司，2：分部,3:区域，4：门店）
     */
    private List<Integer> orgTypeList;

    /**
     * 门店类型：机构分类 (1-集团；2-分子公司；3-仓库；4-数据中心；5-部门；6-加盟商；7-直营店)
     */
    private List<Integer> storeTypeList;

    /**
     * 组织类型 1-公司、2-门店
     */
    private Integer type;

}

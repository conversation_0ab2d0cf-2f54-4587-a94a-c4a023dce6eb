package com.yxt.lotprice.service.model.dto.third.response;

import lombok.Data;

/**
 * @Author: meng<PERSON><PERSON><PERSON>
 * @Date: 2024-09-03  10:55
 * 查询下级组织返回类(根据分公司、分部查询下面所属门店)
 */

@Data
public class QuerySubComResp {

    /**
     * orCode	公司erpCode
     */
    private String  orCode;
    /**
     * orName	名称
     */
    private String orName;
    /**
     * type	机构类型（1：公司，2：分部,3:区域，4：门店）
     */
    private String type;
    /**
     * or_parent 上级机构
     */
    private String orParent;

    /**
     * 全路径
     */
    private String parentPath;


    /**
     * 组织类型 1-公司，2-门店
     */
    private Integer orgType;

}

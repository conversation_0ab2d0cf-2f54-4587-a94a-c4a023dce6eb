package com.yxt.lotprice.service.model.dto.third.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysOrganizationUnitTreeDTO implements Serializable {

    private String id;

    private String orParent;
    private String merCode;
    private String orCode;
    private String orName;
    private Integer orType;
    private Integer orClass;
    private Integer status;
    private String remark;
    private String headPerson;
    private String phone;
    private Integer sortNumber;
    private String shortName;
    private Integer orTag;
    private String crmCode;
    private String fnumber;
    /**
     * 组织层级：1-省公司，2-分部，3-区域，4-门店
     */
    private String layer;
    /**
     * 业务单元编码
     */
    private String unitCode;
    /**
     * 业务单元名称
     */
    private String unitName;
    private Integer isvalid;
    private String createName;
    private Date createTime;
    private String modifyName;
    private Date modifyTime;
    /**
     * 组织节点全路径（id拼接）
     */
    private String parentPath;

    @JsonAlias(value = "children")
    List<SysOrganizationUnitTreeDTO> child;
}
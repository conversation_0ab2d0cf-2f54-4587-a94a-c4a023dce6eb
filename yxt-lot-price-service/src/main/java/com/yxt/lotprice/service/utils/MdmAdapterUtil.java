package com.yxt.lotprice.service.utils;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;

public class MdmAdapterUtil {
    public static void copyResponseBase(com.yxt.lang.dto.api.ResponseBase<?> source, ResponseBase<?> target) {
        target.setCode(source.getCode());
        target.setMsg(source.getMsg());
        target.setTimestamp(source.getTimestamp());
    }
    
    public static void copyPageDTO(com.yxt.lang.dto.api.PageDTO<?> source, PageDTO<?> target) {
        target.setTotalCount(source.getTotalCount());
        target.setTotalPage(source.getTotalPage());
        target.setCurrentPage(source.getCurrentPage());
        target.setPageSize(source.getPageSize());
    }
}
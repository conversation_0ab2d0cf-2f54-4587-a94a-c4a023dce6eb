package com.yxt.lotprice.service.utils;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/5/20 10:53
 */
@Component
public class SequenceUtil {

    private RedisTemplate redisTemplate;

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
    private static final String BP_KEY_PREFIX = "bp_code_seq:";
    private static final String ZN_KEY_PREFIX = "zn_code_seq:";
    public String getBPSequence() {
        String dateStr = dateFormat.format(new Date());
        String redisKey = BP_KEY_PREFIX+dateStr;
        Long seqNum = redisTemplate.opsForValue().increment(redisKey);
        // 如果是新创建的key，设置过期时间为1天(避免无用key堆积)
        if (seqNum != null && seqNum == 1L) {
            redisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
        }
        String seqStr = String.format("%04d", seqNum % 10000);
        return "BP" + dateStr + seqStr;
    }

    public String getZNSequence() {
        String dateStr = dateFormat.format(new Date());
        String redisKey = ZN_KEY_PREFIX+dateStr;
        Long seqNum = redisTemplate.opsForValue().increment(redisKey);
        // 如果是新创建的key，设置过期时间为1天(避免无用key堆积)
        if (seqNum != null && seqNum == 1L) {
            redisTemplate.expire(redisKey, 1, TimeUnit.DAYS);
        }
        String seqStr = String.format("%04d", seqNum % 10000);
        return "ZN" + dateStr + seqStr;
    }
}
